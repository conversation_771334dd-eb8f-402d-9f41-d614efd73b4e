import { apiClient } from '@/api/client'
import { WorkoutTemplateModel, RecommendationModel } from '@/types/api'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import { logger } from '@/utils/logger'
import type {
  GetUserWorkoutProgramTimeZoneInfoResponse,
  GetRecommendationForExerciseRequest,
} from './workout-types'

// Export request/response types
export type {
  GetUserWorkoutProgramTimeZoneInfoResponse,
  GetRecommendationForExerciseRequest,
} from './workout-types'

/**
 * Get user's workout program information with timezone
 */
export async function getUserWorkoutProgramInfo(): Promise<GetUserWorkoutProgramTimeZoneInfoResponse | null> {
  try {
    // Create timezone info object matching API expectations
    const timeZoneInfo = {
      TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
      Offset: new Date().getTimezoneOffset() / -60,
      IsDaylightSaving: false, // Simple implementation
    }

    const response = await apiClient.post(
      '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
      timeZoneInfo
    )

    // Log response for debugging in development
    logger.debug('GetUserWorkoutProgramTimeZoneInfo response:', {
      statusCode: response.data?.StatusCode || response.data?.statusCode,
      hasResult: !!response.data?.Result || response.data?.hasResult,
      hasData:
        response.data?.hasData !== undefined
          ? response.data.hasData
          : !!response.data?.Data,
      dataKeys: response.data ? Object.keys(response.data) : [],
      fullResponse: response.data,
    })

    // Note: hasData: false doesn't mean the user has no program
    // It might just mean certain data isn't available in this specific response
    // The response might still contain workout ID and basic info

    // Handle the specific case where hasData: false but Result/result contains program info
    if (
      response.data &&
      response.data.hasData === false &&
      (response.data.Result || response.data.result)
    ) {
      // The Result/result might still have the workout template ID we need
      const result = response.data.Result || response.data.result
      logger.debug('GetUserWorkoutProgramTimeZoneInfo Result contents:', {
        hasGetUserProgramInfoResponseModel:
          !!result?.GetUserProgramInfoResponseModel,
        nextWorkoutTemplateId:
          result?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Id,
        nextWorkoutTemplateLabel:
          result?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Label,
        fullResult: result,
      })
      return result
    }

    // Handle both wrapped and direct responses (PascalCase and camelCase)
    if (
      response.data &&
      (response.data.StatusCode === 200 || response.data.statusCode === 200) &&
      (response.data.Result || response.data.result)
    ) {
      return response.data.Result || response.data.result
    }

    // Check for other common response formats
    if (response.data && response.data.Data) {
      return response.data.Data
    }

    // If no StatusCode, assume it's a direct response
    if (
      response.data &&
      !response.data.StatusCode &&
      !response.data.statusCode
    ) {
      return response.data
    }

    // Handle root-level data with StatusCode
    if (response.data && response.data.StatusCode === 200) {
      // Extract data excluding StatusCode
      const { StatusCode, ...actualData } = response.data
      // Only return if there's actual data beyond StatusCode
      if (Object.keys(actualData).length > 0) {
        return actualData
      }
    }

    // Log unexpected response format
    logger.warn(
      'Unexpected response format from GetUserWorkoutProgramTimeZoneInfo:',
      response.data
    )
    return null
  } catch (error) {
    logger.error('Error fetching user workout program info:', error)
    throw error
  }
}

/**
 * Get full workout details with exercises
 */
export async function getWorkoutDetails(
  workoutId: number
): Promise<WorkoutTemplateModel | null> {
  try {
    // Based on the implementation guide, this should be a POST request
    const response = await apiClient.post(
      '/api/Workout/GetUserCustomizedCurrentWorkout',
      workoutId // Send the workout ID as the body
    )

    logger.debug('GetUserCustomizedCurrentWorkout response:', response.data)
    // Handle both wrapped and direct responses
    if (response.data.StatusCode === 200 && 'Result' in response.data) {
      return response.data.Result
    }

    // If no StatusCode, assume it's a direct response
    return response.data
  } catch (error) {
    logger.error('Error fetching workout details:', error)
    throw error
  }
}

/**
 * Helper to get username from auth state
 */
function getUsernameFromAuth(): string | null {
  return getCurrentUserEmail()
}

/**
 * Get AI recommendation for an exercise
 */
export async function getExerciseRecommendation(
  request: GetRecommendationForExerciseRequest
): Promise<RecommendationModel | null> {
  // Enhanced logging for debugging
  // eslint-disable-next-line no-console
  console.log('🔍 [getExerciseRecommendation] Called with request:', request)

  try {
    // If username not provided, try to get from auth state
    const username = request.Username?.trim() || getUsernameFromAuth()

    if (!username) {
      // eslint-disable-next-line no-console
      console.error('❌ [getExerciseRecommendation] No username available')
      throw new Error('Username is required for exercise recommendations')
    }

    // Clean username (mobile app pattern: remove spaces and lowercase)
    const cleanUsername = username.replace(/\s+/g, '').toLowerCase()

    // Build complete request body following mobile app pattern exactly
    // Note: API expects null, not undefined for missing values
    const requestBody = {
      Username: cleanUsername,
      ExerciseId: request.ExerciseId,
      WorkoutId: request.WorkoutId,
      IsQuickMode:
        request.IsQuickMode !== undefined ? request.IsQuickMode : null,
      LightSessionDays:
        request.LightSessionDays !== undefined && request.LightSessionDays !== null
          ? request.LightSessionDays
          : null,
      SwapedExId: request.SwapedExId || null,
      IsStrengthPhashe:
        request.IsStrengthPhashe !== undefined
          ? request.IsStrengthPhashe
          : false, // Note: API has typo
      IsFreePlan: request.IsFreePlan !== undefined ? request.IsFreePlan : false,
      IsFirstWorkoutOfStrengthPhase:
        request.IsFirstWorkoutOfStrengthPhase !== undefined
          ? request.IsFirstWorkoutOfStrengthPhase
          : false,
      VersionNo: request.VersionNo !== undefined ? request.VersionNo : 1,
      SetStyle: request.SetStyle || 'Normal', // Default to Normal if not specified
      IsFlexibility:
        request.IsFlexibility !== undefined ? request.IsFlexibility : false,
    }

    // Determine endpoint based on SetStyle and exercise type (following mobile app pattern)
    const setStyle = request.SetStyle?.toLowerCase() || 'normal'
    const isRestPause = setStyle === 'restpause' || setStyle === 'rest-pause'

    // Force normal endpoint for flexibility exercises (mobile app logic)
    const shouldUseNormal =
      request.IsFlexibility ||
      request.ExerciseId === 16508 || // specific exercise ID
      !isRestPause

    const endpoint = shouldUseNormal
      ? '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
      : '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'

    // eslint-disable-next-line no-console
    console.log(`🚀 [API] Making request to ${endpoint}`)
    // eslint-disable-next-line no-console
    console.log(`📤 [API] Request body:`, JSON.stringify(requestBody, null, 2))
    // eslint-disable-next-line no-console
    console.log(
      `🎯 [API] SetStyle: ${request.SetStyle}, IsFlexibility: ${request.IsFlexibility} -> Using ${shouldUseNormal ? 'Normal' : 'RestPause'} endpoint`
    )

    const response = await apiClient.post(endpoint, requestBody)

    // eslint-disable-next-line no-console
    console.log(`📥 [API] Response status:`, response.status)
    // eslint-disable-next-line no-console
    console.log(
      `📥 [API] Response data:`,
      JSON.stringify(response.data, null, 2)
    )

    // Check if response is null
    if (
      response.data === null ||
      (response.data?.Result === null && response.data?.StatusCode === 200)
    ) {
      // eslint-disable-next-line no-console
      console.log(
        `⚠️ [API] Null recommendation received for exercise ${request.ExerciseId} - no exercise history`
      )
      return null
    }

    // eslint-disable-next-line no-console
    console.log(
      `🏋️ [API] Weight in response:`,
      response.data?.Weight || response.data?.Result?.Weight || 'NOT FOUND'
    )

    logger.debug(`${endpoint} response:`, response.data)

    // Handle both wrapped and direct responses
    if (response.data.StatusCode === 200 && 'Result' in response.data) {
      // eslint-disable-next-line no-console
      console.log('✅ [API] Returning wrapped response Result')
      return response.data.Result
    }

    // If no StatusCode, assume it's a direct response
    // eslint-disable-next-line no-console
    console.log('✅ [API] Returning direct response')
    return response.data
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ [getExerciseRecommendation] Error:', error)
    logger.error('Error fetching exercise recommendation:', error)
    // Return null instead of throwing to allow graceful degradation
    return null
  }
}

/**
 * Generate cache key for a recommendation
 * This will be used by the cache service
 */
export function generateRecommendationCacheKey(
  userId: string,
  exerciseId: number,
  workoutId: number
): string {
  return `${userId}-${exerciseId}-${workoutId}`
}
