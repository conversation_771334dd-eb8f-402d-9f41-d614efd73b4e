'use client'

import { useState } from 'react'
import { getExerciseRecommendation } from '@/services/api/workout'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import { useAuthStore } from '@/stores/authStore'
import { apiClient } from '@/api/client'

export default function DebugApiPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [authInfo, setAuthInfo] = useState<any>(null)
  const authStore = useAuthStore()

  const checkAuthStatus = async () => {
    try {
      // Check auth store
      const authStoreInfo = {
        isAuthenticated: authStore.isAuthenticated,
        hasToken: !!authStore.token,
        tokenPreview: authStore.token?.substring(0, 20) + '...',
        userEmail: authStore.user?.email,
      }

      // Check API client headers
      const authHeader = apiClient.defaults.headers.common['Authorization']

      // Check server-side token
      const tokenResponse = await fetch('/api/auth/token', {
        credentials: 'include',
      })
      const tokenData = await tokenResponse.json()

      setAuthInfo({
        authStore: authStoreInfo,
        apiClientHeader: authHeader,
        serverToken: tokenData,
      })

      console.log('🔍 Auth Status:', {
        authStore: authStoreInfo,
        apiClientHeader: authHeader,
        serverToken: tokenData,
      })
    } catch (err) {
      console.error('❌ Auth check error:', err)
      setError(err instanceof Error ? err.message : 'Auth check failed')
    }
  }

  const testRecommendationAPI = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const userEmail = getCurrentUserEmail()
      if (!userEmail) {
        throw new Error('No user email found - please login first')
      }

      console.log('🔍 Testing recommendation API with user:', userEmail)

      // Test with the same exercise from our direct test
      const request = {
        Username: userEmail,
        ExerciseId: 12980, // Airborne Lunge
        WorkoutId: 14019, // Bodyweight 4 workout
        SetStyle: 'Normal',
        IsFlexibility: false,
        IsQuickMode: null,
        LightSessionDays: null,
        SwapedExId: undefined,
        IsStrengthPhashe: false, // Note: API has typo
        IsFreePlan: false,
        IsFirstWorkoutOfStrengthPhase: false,
        VersionNo: 1,
      }

      console.log('📤 Request:', request)

      const recommendation = await getExerciseRecommendation(request)
      
      console.log('📥 Response:', recommendation)
      setResult(recommendation)

      if (recommendation?.Weight && recommendation?.Reps) {
        console.log('✅ SUCCESS: Got weight and reps!')
      } else {
        console.log('❌ ISSUE: Missing weight or reps')
      }

    } catch (err) {
      console.error('❌ Error:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Debug Exercise Recommendation API</h1>
      
      <div className="space-x-4">
        <button
          onClick={checkAuthStatus}
          className="bg-green-500 text-white px-4 py-2 rounded"
        >
          Check Auth Status
        </button>
        <button
          onClick={testRecommendationAPI}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Recommendation API'}
        </button>
      </div>

      {authInfo && (
        <div className="mt-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded">
          <h3 className="font-bold">Auth Status:</h3>
          <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
            {JSON.stringify(authInfo, null, 2)}
          </pre>
        </div>
      )}

      {error && (
        <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <h3 className="font-bold">Error:</h3>
          <p>{error}</p>
        </div>
      )}

      {result && (
        <div className="mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
          <h3 className="font-bold">Success! Recommendation received:</h3>
          <div className="mt-2">
            <p><strong>Weight:</strong> {result.Weight ? `${result.Weight.Kg} kg (${result.Weight.Lb.toFixed(1)} lbs)` : 'Not found'}</p>
            <p><strong>Reps:</strong> {result.Reps || 'Not found'}</p>
            <p><strong>Sets:</strong> {result.Series || 'Not found'}</p>
          </div>
          <details className="mt-4">
            <summary className="cursor-pointer font-semibold">Full Response (click to expand)</summary>
            <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  )
}
